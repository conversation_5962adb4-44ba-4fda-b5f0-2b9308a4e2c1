#!/usr/bin/env python3
"""
External Model Loader for BulldozerPriceGenius
Handles loading ML models from external storage (Google Drive) for Heroku deployment
"""

import os
import pickle
import joblib
import streamlit as st
import requests
from pathlib import Path
import tempfile
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExternalModelLoader:
    """
    Handles loading ML models from external storage sources.
    Supports Google Drive direct download links and local fallback.
    """
    
    def __init__(self):
        self.cache_dir = Path(tempfile.gettempdir()) / "bpg_models"
        self.cache_dir.mkdir(exist_ok=True)
        
        # Model URLs - these should be direct download links
        self.model_urls = {
            'randomforest_regressor': {
                'url': 'https://drive.google.com/uc?export=download&id=YOUR_GOOGLE_DRIVE_FILE_ID',
                'filename': 'randomforest_regressor_best_RMSLE.pkl',
                'local_path': 'src/models/randomforest_regressor_best_RMSLE.pkl'
            },
            'preprocessing_components': {
                'url': 'https://drive.google.com/uc?export=download&id=YOUR_PREPROCESSING_FILE_ID',
                'filename': 'preprocessing_components.pkl',
                'local_path': 'src/models/preprocessing_components.pkl'
            }
        }
    
    def download_model(self, model_key, max_retries=3):
        """
        Download model from external storage with retry logic.
        
        Args:
            model_key (str): Key identifying the model to download
            max_retries (int): Maximum number of download attempts
            
        Returns:
            str: Path to downloaded model file, or None if failed
        """
        if model_key not in self.model_urls:
            logger.error(f"Unknown model key: {model_key}")
            return None
            
        model_info = self.model_urls[model_key]
        cache_path = self.cache_dir / model_info['filename']
        
        # Check if already cached
        if cache_path.exists():
            logger.info(f"Using cached model: {cache_path}")
            return str(cache_path)
        
        # Try downloading from external URL
        for attempt in range(max_retries):
            try:
                logger.info(f"Downloading {model_key} (attempt {attempt + 1}/{max_retries})")
                
                response = requests.get(model_info['url'], timeout=30)
                response.raise_for_status()
                
                # Save to cache
                with open(cache_path, 'wb') as f:
                    f.write(response.content)
                
                logger.info(f"Successfully downloaded {model_key} to {cache_path}")
                return str(cache_path)
                
            except Exception as e:
                logger.warning(f"Download attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"Failed to download {model_key} after {max_retries} attempts")
        
        return None
    
    def load_model(self, model_key, use_joblib=False):
        """
        Load model from external storage or local fallback.
        
        Args:
            model_key (str): Key identifying the model to load
            use_joblib (bool): Whether to use joblib instead of pickle
            
        Returns:
            object: Loaded model object, or None if failed
        """
        model_info = self.model_urls.get(model_key)
        if not model_info:
            logger.error(f"Unknown model key: {model_key}")
            return None
        
        # Try local file first (for development)
        local_path = model_info['local_path']
        if os.path.exists(local_path):
            try:
                logger.info(f"Loading model from local path: {local_path}")
                if use_joblib:
                    return joblib.load(local_path)
                else:
                    with open(local_path, 'rb') as f:
                        return pickle.load(f)
            except Exception as e:
                logger.warning(f"Failed to load local model: {e}")
        
        # Try downloading from external storage
        downloaded_path = self.download_model(model_key)
        if downloaded_path:
            try:
                logger.info(f"Loading downloaded model: {downloaded_path}")
                if use_joblib:
                    return joblib.load(downloaded_path)
                else:
                    with open(downloaded_path, 'rb') as f:
                        return pickle.load(f)
            except Exception as e:
                logger.error(f"Failed to load downloaded model: {e}")
        
        logger.error(f"Could not load model: {model_key}")
        return None
    
    def get_model_info(self, model_key):
        """
        Get information about a model's availability and source.
        
        Args:
            model_key (str): Key identifying the model
            
        Returns:
            dict: Model availability information
        """
        model_info = self.model_urls.get(model_key)
        if not model_info:
            return {'available': False, 'source': 'unknown', 'error': 'Unknown model key'}
        
        local_path = model_info['local_path']
        cache_path = self.cache_dir / model_info['filename']
        
        # Check local availability
        if os.path.exists(local_path):
            return {
                'available': True,
                'source': 'local',
                'path': local_path,
                'size_mb': round(os.path.getsize(local_path) / (1024*1024), 2)
            }
        
        # Check cache availability
        if cache_path.exists():
            return {
                'available': True,
                'source': 'cached',
                'path': str(cache_path),
                'size_mb': round(os.path.getsize(cache_path) / (1024*1024), 2)
            }
        
        # Check if downloadable
        return {
            'available': False,
            'source': 'external',
            'url': model_info['url'],
            'downloadable': True
        }

# Global instance for easy access
model_loader = ExternalModelLoader()

def load_ml_model(model_key='randomforest_regressor'):
    """
    Convenience function to load the main ML model.
    
    Args:
        model_key (str): Model identifier
        
    Returns:
        object: Loaded model or None
    """
    return model_loader.load_model(model_key, use_joblib=True)

def load_preprocessing_components(model_key='preprocessing_components'):
    """
    Convenience function to load preprocessing components.
    
    Args:
        model_key (str): Preprocessing components identifier
        
    Returns:
        object: Loaded preprocessing components or None
    """
    return model_loader.load_model(model_key, use_joblib=False)

def create_mock_model():
    """
    Create a mock model for demonstration when real model is unavailable.
    
    Returns:
        object: Mock model with predict method
    """
    class MockModel:
        def __init__(self):
            self.feature_importances_ = [0.199, 0.155, 0.128, 0.095, 0.087]  # Sample importances
            self.n_features_in_ = 50
        
        def predict(self, X):
            """Mock prediction based on simple heuristics"""
            import numpy as np
            
            # Simple heuristic: base price + year factor + size factor
            if hasattr(X, 'shape'):
                n_samples = X.shape[0]
            else:
                n_samples = len(X)
            
            # Generate realistic bulldozer prices (20k - 80k range)
            base_prices = np.random.normal(45000, 15000, n_samples)
            base_prices = np.clip(base_prices, 15000, 100000)
            
            return base_prices
    
    return MockModel()

def _validate_real_model(model):
    """
    Validate that a model is a real trained model, not a mock.

    Args:
        model: Model object to validate

    Returns:
        bool: True if model is real, False if mock or invalid
    """
    if model is None:
        return False

    # Check for RandomForest specific attributes
    if hasattr(model, 'n_estimators') and hasattr(model, 'predict'):
        # Real RandomForest should have reasonable number of estimators
        if hasattr(model, 'n_estimators') and model.n_estimators > 10:
            return True

    # Check for sklearn model attributes
    if hasattr(model, 'predict') and hasattr(model, '__class__'):
        class_name = model.__class__.__name__
        # Avoid mock models (they typically have simple class names)
        if 'Mock' not in class_name and 'sklearn' in str(model.__class__.__module__):
            return True

    return False

def _validate_real_preprocessing(preprocessing):
    """
    Validate that preprocessing components are real, not mock.

    Args:
        preprocessing: Preprocessing object to validate

    Returns:
        bool: True if preprocessing is real, False if mock or invalid
    """
    if preprocessing is None:
        return False

    # Real preprocessing should be a dict with specific keys
    if isinstance(preprocessing, dict):
        expected_keys = ['label_encoders', 'imputer', 'performance_metrics']
        # Check if it has at least some expected keys
        if any(key in preprocessing for key in expected_keys):
            return True

        # Alternative: check for reasonable number of components
        if len(preprocessing) >= 2:
            return True

    return False

def create_mock_preprocessing():
    """
    Create mock preprocessing components for demonstration.
    
    Returns:
        dict: Mock preprocessing components
    """
    class MockTransformer:
        def transform(self, X):
            return X
        
        def fit_transform(self, X):
            return X
    
    return {
        'imputer': MockTransformer(),
        'scaler': MockTransformer(),
        'encoder': MockTransformer()
    }

# Streamlit caching for model loading with automatic validation
@st.cache_resource
def get_cached_model():
    """Load and cache the ML model for Streamlit with automatic validation and retry."""
    model = load_ml_model()

    # Automatic validation: Check if model is real or mock
    if model is None or not _validate_real_model(model):
        logger.warning("Model validation failed - attempting cache clear and retry")

        # Clear cache and retry once
        try:
            st.cache_resource.clear()
            logger.info("Cache cleared, retrying model load")
            model = load_ml_model()

            # Validate retry result
            if model is None or not _validate_real_model(model):
                logger.error("Model validation failed after cache clear and retry")
                st.error("❌ Failed to load ML model. Please check model files.")
                model = create_mock_model()
            else:
                logger.info("Model validation successful after cache clear")
                st.success(f"✅ Real RandomForest model loaded with {model.n_estimators} estimators")
        except Exception as e:
            logger.error(f"Error during cache clear and retry: {e}")
            st.error("❌ Failed to load ML model. Please check model files.")
            model = create_mock_model()
    else:
        logger.info(f"Model validation successful: {type(model)}")
        if hasattr(model, 'n_estimators'):
            st.success(f"✅ Real RandomForest model loaded with {model.n_estimators} estimators")
        else:
            st.success("✅ ML model loaded successfully")

    return model

@st.cache_resource
def get_cached_preprocessing():
    """Load and cache preprocessing components for Streamlit with automatic validation and retry."""
    preprocessing = load_preprocessing_components()

    # Automatic validation: Check if preprocessing is real or mock
    if preprocessing is None or not _validate_real_preprocessing(preprocessing):
        logger.warning("Preprocessing validation failed - attempting cache clear and retry")

        # Clear cache and retry once
        try:
            st.cache_resource.clear()
            logger.info("Cache cleared, retrying preprocessing load")
            preprocessing = load_preprocessing_components()

            # Validate retry result
            if preprocessing is None or not _validate_real_preprocessing(preprocessing):
                logger.error("Preprocessing validation failed after cache clear and retry")
                st.error("❌ Failed to load preprocessing components. Please check model files.")
                preprocessing = create_mock_preprocessing()
            else:
                logger.info("Preprocessing validation successful after cache clear")
                st.success(f"✅ Real preprocessing components loaded with {len(preprocessing)} components")
        except Exception as e:
            logger.error(f"Error during preprocessing cache clear and retry: {e}")
            st.error("❌ Failed to load preprocessing components. Please check model files.")
            preprocessing = create_mock_preprocessing()
    else:
        logger.info(f"Preprocessing validation successful: {type(preprocessing)}")
        if isinstance(preprocessing, dict):
            st.success(f"✅ Real preprocessing components loaded with {len(preprocessing)} components")
        else:
            st.success("✅ Preprocessing components loaded successfully")

    return preprocessing
