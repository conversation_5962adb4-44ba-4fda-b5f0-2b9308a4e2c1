#!/usr/bin/env python3
"""
Test for MediaFileStorageError fix in Hypothesis and Validation page
Tests the safe image loading functionality with various scenarios
"""

import os
import sys
import tempfile
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

# Mock Streamlit for testing
class MockStreamlit:
    def __init__(self):
        self.output = []
    
    def image(self, path, caption=""):
        self.output.append(f"IMAGE: {path} - {caption}")
        return True
    
    def warning(self, text):
        self.output.append(f"WARNING: {text}")
    
    def info(self, text):
        self.output.append(f"INFO: {text}")
    
    def error(self, text):
        self.output.append(f"ERROR: {text}")

# Set up mock Streamlit
mock_st = MockStreamlit()
sys.modules['streamlit'] = mock_st

# Add app_pages to path
sys.path.append('../app_pages')

def test_safe_image_loading():
    """Test the safe_load_image function with various scenarios"""
    
    print("🧪 Testing Safe Image Loading Fix")
    print("=" * 50)
    
    # Import the function
    try:
        from two_hypothesis_and_validation import safe_load_image
        print("✅ Successfully imported safe_load_image function")
    except ImportError as e:
        print(f"❌ Failed to import safe_load_image: {e}")
        return False
    
    # Test 1: File exists scenario
    print("\n📋 Test 1: File exists scenario")
    with tempfile.NamedTemporaryFile(suffix='.webp', delete=False) as tmp_file:
        tmp_path = tmp_file.name
        tmp_file.write(b"fake image data")
    
    try:
        mock_st.output.clear()
        result = safe_load_image(tmp_path, "Test Caption", "Test fallback")
        print(f"   Result: {result}")
        print(f"   Output: {mock_st.output}")
        assert result == True, "Should return True when file exists"
        assert any("IMAGE:" in output for output in mock_st.output), "Should call st.image"
        print("   ✅ PASSED: File exists scenario")
    finally:
        os.unlink(tmp_path)
    
    # Test 2: File doesn't exist scenario
    print("\n📋 Test 2: File doesn't exist scenario")
    mock_st.output.clear()
    result = safe_load_image("nonexistent_file.webp", "Test Caption", "Custom fallback message")
    print(f"   Result: {result}")
    print(f"   Output: {mock_st.output}")
    assert result == False, "Should return False when file doesn't exist"
    assert any("Custom fallback message" in output for output in mock_st.output), "Should show fallback message"
    print("   ✅ PASSED: File doesn't exist scenario")
    
    # Test 3: MediaFileStorageError simulation
    print("\n📋 Test 3: MediaFileStorageError simulation")
    mock_st.output.clear()
    
    # Mock st.image to raise MediaFileStorageError
    original_image = mock_st.image
    def mock_image_error(*args, **kwargs):
        raise Exception("MediaFileStorageError: Unable to access file")
    
    mock_st.image = mock_image_error
    
    try:
        result = safe_load_image("results/sale_price.webp", "Test Caption", "Storage error fallback")
        print(f"   Result: {result}")
        print(f"   Output: {mock_st.output}")
        assert result == False, "Should return False when MediaFileStorageError occurs"
        assert any("Storage error fallback" in output for output in mock_st.output), "Should show storage error fallback"
        print("   ✅ PASSED: MediaFileStorageError scenario")
    finally:
        mock_st.image = original_image
    
    print("\n🎉 All tests passed! Safe image loading fix is working correctly.")
    return True

def test_hypothesis_page_integration():
    """Test that the hypothesis page can be imported without errors"""
    
    print("\n🧪 Testing Hypothesis Page Integration")
    print("=" * 50)
    
    try:
        from two_hypothesis_and_validation import hypothesis_and_validation_body
        print("✅ Successfully imported hypothesis_and_validation_body function")
        
        # Test that the function can be called (though we won't run the full Streamlit app)
        print("✅ Function is callable and ready for deployment")
        return True
    except ImportError as e:
        print(f"❌ Failed to import hypothesis page: {e}")
        return False
    except Exception as e:
        print(f"❌ Error in hypothesis page: {e}")
        return False

def test_image_files_exist():
    """Test that the required image files exist in the results directory"""
    
    print("\n🧪 Testing Image Files Existence")
    print("=" * 50)
    
    required_images = [
        "results/sale_price.webp",
        "results/feature_importance.webp", 
        "results/model_comparison.webp"
    ]
    
    all_exist = True
    for image_path in required_images:
        if os.path.exists(image_path):
            print(f"   ✅ {image_path} - EXISTS")
        else:
            print(f"   ❌ {image_path} - MISSING")
            all_exist = False
    
    if all_exist:
        print("\n🎉 All required image files exist!")
    else:
        print("\n⚠️  Some image files are missing, but safe loading will handle this gracefully.")
    
    return all_exist

if __name__ == "__main__":
    print("🚀 Running MediaFileStorageError Fix Tests")
    print("=" * 60)
    
    # Run all tests
    test1_passed = test_safe_image_loading()
    test2_passed = test_hypothesis_page_integration()
    test3_passed = test_image_files_exist()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY:")
    print(f"   Safe Image Loading: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Page Integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"   Image Files Exist: {'✅ PASSED' if test3_passed else '⚠️  PARTIAL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 MediaFileStorageError fix is ready for deployment!")
        print("   The page will now handle missing images gracefully.")
    else:
        print("\n❌ Some tests failed. Please review the implementation.")
