#!/usr/bin/env python3
"""
Analyze BulldozerPriceGenius deployment size to optimize for Heroku's 300MB limit
"""

import os
import sys
from pathlib import Path

def get_directory_size(path):
    """Calculate total size of directory in bytes"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
    except (OSError, FileNotFoundError):
        pass
    return total_size

def format_size(size_bytes):
    """Format size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def check_slugignore_effectiveness():
    """Check what files would be excluded by .slugignore"""
    excluded_patterns = []
    included_size = 0
    excluded_size = 0
    
    if os.path.exists('.slugignore'):
        with open('.slugignore', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    excluded_patterns.append(line)
    
    print("📋 Deployment Size Analysis")
    print("=" * 50)
    
    # Analyze major directories
    directories = ['data', 'docs', 'jupyter_notebooks', 'tests', 'examples', 
                  'results', 'static', 'myenv', '.git', '__pycache__']
    
    total_project_size = 0
    excluded_dirs_size = 0
    
    for directory in directories:
        if os.path.exists(directory):
            dir_size = get_directory_size(directory)
            total_project_size += dir_size
            
            # Check if directory would be excluded
            excluded = any(pattern.rstrip('/') == directory or 
                          pattern == f"{directory}/" for pattern in excluded_patterns)
            
            status = "❌ EXCLUDED" if excluded else "✅ INCLUDED"
            if excluded:
                excluded_dirs_size += dir_size
            
            print(f"{directory:20} {format_size(dir_size):>10} {status}")
    
    # Analyze individual large files
    print("\n📁 Large Files Analysis:")
    print("-" * 30)
    
    large_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            filepath = os.path.join(root, file)
            try:
                size = os.path.getsize(filepath)
                if size > 1024 * 1024:  # Files larger than 1MB
                    large_files.append((filepath, size))
            except (OSError, FileNotFoundError):
                pass
    
    large_files.sort(key=lambda x: x[1], reverse=True)
    
    for filepath, size in large_files[:10]:  # Top 10 largest files
        # Check if file would be excluded
        excluded = False
        for pattern in excluded_patterns:
            if pattern in filepath or filepath.endswith(pattern.lstrip('*')):
                excluded = True
                break
        
        status = "❌ EXCLUDED" if excluded else "✅ INCLUDED"
        print(f"{filepath:40} {format_size(size):>10} {status}")
    
    # Calculate estimated deployment size
    print("\n🎯 Deployment Size Estimation:")
    print("-" * 35)
    
    # Core application files
    core_files = ['app.py', 'requirements.txt', 'Procfile', '.python-version', 'setup.sh']
    core_dirs = ['app_pages', 'src']
    
    core_size = 0
    for file in core_files:
        if os.path.exists(file):
            core_size += os.path.getsize(file)
    
    for directory in core_dirs:
        if os.path.exists(directory):
            core_size += get_directory_size(directory)
    
    # Estimate Python dependencies size (typical for our requirements)
    estimated_deps_size = 200 * 1024 * 1024  # ~200MB for streamlit + ML libraries
    
    estimated_total = core_size + estimated_deps_size
    
    print(f"Core Application Files: {format_size(core_size)}")
    print(f"Python Dependencies:    {format_size(estimated_deps_size)} (estimated)")
    print(f"Total Estimated Size:   {format_size(estimated_total)}")
    print(f"Heroku Soft Limit:      300.0 MB")
    
    if estimated_total < 300 * 1024 * 1024:
        print("✅ WITHIN LIMIT")
        margin = (300 * 1024 * 1024) - estimated_total
        print(f"Safety Margin:          {format_size(margin)}")
    else:
        print("❌ EXCEEDS LIMIT")
        excess = estimated_total - (300 * 1024 * 1024)
        print(f"Excess Size:            {format_size(excess)}")
    
    print(f"\nExcluded by .slugignore: {format_size(excluded_dirs_size)}")
    
    return estimated_total < 300 * 1024 * 1024

def main():
    """Main analysis function"""
    print("🔍 BulldozerPriceGenius Deployment Size Optimization")
    print("=" * 60)
    
    success = check_slugignore_effectiveness()
    
    print("\n📋 Optimization Recommendations:")
    print("-" * 40)
    
    if success:
        print("✅ Deployment size should be within Heroku's 300MB limit")
        print("✅ .slugignore configuration appears effective")
        print("✅ Ready for optimized deployment")
    else:
        print("⚠️  Additional optimization may be needed")
        print("💡 Consider removing more dependencies from requirements.txt")
        print("💡 Ensure all large files are properly excluded")
    
    print("\n🚀 Next Steps:")
    print("1. Deploy to Heroku and monitor actual slug size")
    print("2. Verify all 8 test scenarios still pass")
    print("3. Monitor boot time performance improvements")

if __name__ == "__main__":
    main()
