#!/usr/bin/env python3
"""
Test that the matplotlib dependency has been successfully removed
"""

def test_matplotlib_removal():
    """Test that the application works without matplotlib"""
    
    print("Testing Application After Matplotlib Removal")
    print("=" * 50)
    
    try:
        # Test core imports
        import streamlit as st
        print("✅ streamlit imported successfully")
        
        import pandas as pd
        print("✅ pandas imported successfully")
        
        import io
        print("✅ io imported successfully")
        
        # Test that matplotlib is not imported in the fixed file
        import sys
        sys.path.append('app_pages')
        
        # Read the file content to verify matplotlib is removed
        with open('app_pages/three_project_framework.py', 'r') as f:
            content = f.read()
            
        if 'import matplotlib' in content:
            print("❌ matplotlib import still present in three_project_framework.py")
            return False
        else:
            print("✅ matplotlib import successfully removed")
            
        if 'plt.' in content:
            print("❌ plt usage still present in three_project_framework.py")
            return False
        else:
            print("✅ plt usage successfully removed")
            
        print("\n🎯 Matplotlib dependency successfully eliminated!")
        print("✅ Application should work without matplotlib in requirements.txt")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

if __name__ == "__main__":
    test_matplotlib_removal()
