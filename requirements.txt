# BulldozerPriceGenius - Optimized Heroku Deployment Requirements
# CRITICAL: Minimal dependencies to reduce slug size from 461MB to <300MB

# Core Streamlit framework (ESSENTIAL)
streamlit>=1.18.0,<2.0.0

# Core ML and data processing (ESSENTIAL)
numpy>=1.21.0,<3.0.0
pandas>=1.3.0,<3.0.0
scikit-learn>=1.0.0,<2.0.0

# External model storage (CRITICAL for 561MB model download)
requests>=2.25.0,<3.0.0
gdown==5.2.0

# REMOVED for size optimization:
# - altair (visualization - not essential)
# - matplotlib (visualization - not used in production)
# - seaborn (visualization - not used in production)
# - joblib (included with scikit-learn)
# - tqdm (progress bars - not essential for production)

