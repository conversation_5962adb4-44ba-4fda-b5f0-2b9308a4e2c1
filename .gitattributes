# Only track essential large files with LFS (not for Heroku deployment)
# Note: Large files are now stored externally and loaded at runtime

# Keep small result images and essential static files in regular Git
# *.webp files in results/ and static/ are small and should not use LFS

# Remove LFS tracking for files that will be stored externally:
# - Large datasets (*.csv, *.parquet) -> External storage
# - ML models (*.pkl, *.joblib) -> External storage
# - Raw data files -> External storage

# Only use LFS for truly essential large binary files that must be in repo
# (Currently none - all large files moved to external storage)
