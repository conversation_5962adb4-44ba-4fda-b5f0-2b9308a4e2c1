#!/bin/bash

# Secure Heroku Deployment Script for BulldozerPriceGenius
# This script ensures secure deployment without exposing sensitive information

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

echo "🔒 Secure Heroku Deployment for BulldozerPriceGenius"
echo "=================================================="

# Step 1: Security Audit
echo ""
echo "📋 Step 1: Security Audit"
echo "-------------------------"

# Check for sensitive files
sensitive_files=(".env" ".streamlit/secrets.toml" "kaggle.json" "credentials.json")
for file in "${sensitive_files[@]}"; do
    if [ -f "$file" ]; then
        print_warning "Sensitive file found: $file (should be in .gitignore)"
    else
        print_status "No sensitive file: $file"
    fi
done

# Check .gitignore
if grep -q ".env" .gitignore && grep -q "secrets.toml" .gitignore; then
    print_status ".gitignore properly configured for sensitive files"
else
    print_error ".gitignore missing sensitive file patterns"
    echo "Please ensure .gitignore includes: .env, .streamlit/secrets.toml"
    exit 1
fi

# Step 2: Verify deployment files
echo ""
echo "📋 Step 2: Deployment Files Verification"
echo "----------------------------------------"

required_files=("Procfile" "requirements.txt" ".python-version" "setup.sh" "app.py")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "$file exists"
    else
        print_error "$file is missing"
        exit 1
    fi
done

# Check .slugignore
if [ -f ".slugignore" ]; then
    print_status ".slugignore exists (optimizes deployment size)"
else
    print_warning ".slugignore not found (deployment may be larger)"
fi

# Step 3: Environment Variables Setup
echo ""
echo "📋 Step 3: Environment Variables Setup"
echo "--------------------------------------"

echo "For secure deployment, you need to configure the Google Drive model file ID."
echo "This should be the file ID of your trained RandomForest model (560MB)."
echo ""
echo "Example: If your Google Drive link is:"
echo "https://drive.google.com/file/d/1ABC123DEF456GHI789JKL/view?usp=sharing"
echo "Then your file ID is: 1ABC123DEF456GHI789JKL"
echo ""

read -p "Enter your Google Drive file ID: " FILE_ID

if [ -z "$FILE_ID" ]; then
    print_error "File ID cannot be empty"
    exit 1
fi

if [ "$FILE_ID" = "YOUR_GOOGLE_DRIVE_FILE_ID_HERE" ]; then
    print_error "Please provide your actual Google Drive file ID"
    exit 1
fi

# Step 4: Heroku App Configuration
echo ""
echo "📋 Step 4: Heroku App Configuration"
echo "-----------------------------------"

# Check if Heroku CLI is installed
if ! command -v heroku &> /dev/null; then
    print_error "Heroku CLI is not installed"
    echo "Please install it from: https://devcenter.heroku.com/articles/heroku-cli"
    exit 1
fi

# Check if logged in to Heroku
if ! heroku auth:whoami &> /dev/null; then
    print_error "Not logged in to Heroku"
    echo "Please run: heroku login"
    exit 1
fi

read -p "Enter your Heroku app name (or press Enter to create new): " APP_NAME

if [ -z "$APP_NAME" ]; then
    echo "Creating new Heroku app..."
    APP_NAME=$(heroku create --json | python3 -c "import sys, json; print(json.load(sys.stdin)['name'])")
    print_status "Created new app: $APP_NAME"
else
    # Check if app exists
    if heroku apps:info --app "$APP_NAME" &> /dev/null; then
        print_status "App '$APP_NAME' exists"
    else
        print_error "App '$APP_NAME' does not exist"
        echo "Please create it first: heroku create $APP_NAME"
        exit 1
    fi
fi

# Step 5: Set environment variables securely
echo ""
echo "📋 Step 5: Secure Environment Configuration"
echo "-------------------------------------------"

print_info "Setting GOOGLE_DRIVE_MODEL_ID environment variable..."
heroku config:set GOOGLE_DRIVE_MODEL_ID="$FILE_ID" --app "$APP_NAME"

print_info "Setting STREAMLIT_ENV to production..."
heroku config:set STREAMLIT_ENV="production" --app "$APP_NAME"

if [ $? -eq 0 ]; then
    print_status "Environment variables set successfully"
else
    print_error "Failed to set environment variables"
    exit 1
fi

# Step 6: Final security check
echo ""
echo "📋 Step 6: Final Security Check"
echo "-------------------------------"

# Verify no sensitive data in git
if git log --oneline -10 | grep -i -E "(password|secret|key|token)" > /dev/null; then
    print_warning "Potential sensitive data found in recent git commits"
    echo "Please review your commit history for sensitive information"
fi

# Check current git status
if [ -n "$(git status --porcelain)" ]; then
    print_info "Uncommitted changes detected. Committing for deployment..."
    git add .
    git commit -m "Secure deployment preparation - $(date)"
fi

# Step 7: Deploy
echo ""
echo "📋 Step 7: Secure Deployment"
echo "----------------------------"

print_info "Deploying to Heroku..."
git push heroku main

if [ $? -eq 0 ]; then
    print_status "Deployment successful!"
else
    print_error "Deployment failed"
    echo "Check the logs: heroku logs --tail --app $APP_NAME"
    exit 1
fi

# Step 8: Post-deployment verification
echo ""
echo "📋 Step 8: Post-deployment Security Verification"
echo "------------------------------------------------"

print_info "Verifying environment variables are set..."
heroku config --app "$APP_NAME" | grep -E "(GOOGLE_DRIVE_MODEL_ID|STREAMLIT_ENV)"

print_info "Checking app status..."
heroku ps --app "$APP_NAME"

echo ""
echo "🎉 Secure Deployment Complete!"
echo "=============================="
print_status "App URL: https://$APP_NAME.herokuapp.com"
print_status "App Name: $APP_NAME"
print_status "Environment variables configured securely"
print_status "No sensitive data exposed in deployment"

echo ""
echo "📋 Security Checklist Completed:"
echo "✅ Sensitive files excluded from deployment"
echo "✅ Environment variables configured securely"
echo "✅ No hardcoded credentials in codebase"
echo "✅ Model files handled via external storage"
echo "✅ Production environment configured"

echo ""
echo "📋 Next Steps:"
echo "- Test the Interactive Prediction page"
echo "- Monitor logs: heroku logs --tail --app $APP_NAME"
echo "- Verify model loading works correctly"
echo "- Test all application features"
