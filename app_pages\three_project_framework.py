# Import required libraries
import streamlit as st
import pandas as pd
import io
import os


# ===== HELPER FUNCTIONS =====

def safe_load_image(image_path, caption="", fallback_message=None):
    """
    Safely load an image with error handling for Heroku deployment.

    Args:
        image_path (str): Path to the image file
        caption (str): Caption for the image
        fallback_message (str): Message to display if image cannot be loaded
    """
    try:
        # Check if file exists
        if os.path.exists(image_path):
            st.image(image_path, caption=caption)
            return True
        else:
            # File doesn't exist - show fallback
            if fallback_message:
                st.warning(f"📊 {fallback_message}")
            else:
                st.warning(f"📊 Visualization not available: {os.path.basename(image_path)}")
            return False
    except Exception as e:
        # Handle any other errors (like MediaFileStorageError)
        error_msg = str(e)
        if "MediaFileStorageError" in error_msg or "storage" in error_msg.lower():
            # Specific handling for Heroku storage issues
            if fallback_message:
                st.info(f"📊 {fallback_message}")
            else:
                st.info(f"📊 Chart data available but visualization temporarily unavailable due to deployment constraints.")
        else:
            # Generic error handling
            st.error(f"⚠️ Unable to load visualization: {os.path.basename(image_path)}")
        return False
# Function to load data from a CSV file
def load_data(csv_file_path, nrows=None):
    return pd.read_csv(csv_file_path, nrows=nrows)


# Function to load data from a Parquet file
def load_parquet_data(parquet_file_path, nrows=None):
    return pd.read_parquet(parquet_file_path)


def safe_load_data(primary_path, fallback_paths=None, nrows=None, description="dataset"):
    """
    Safely load data with comprehensive fallback strategy for Heroku deployment.

    Args:
        primary_path (str): Primary file path to attempt loading
        fallback_paths (list): List of fallback file paths to try
        nrows (int): Number of rows to load (None for all)
        description (str): Description of the dataset for error messages

    Returns:
        tuple: (dataframe, success_message, error_message)
    """
    if fallback_paths is None:
        fallback_paths = []

    # Try primary path first
    all_paths = [primary_path] + fallback_paths

    for i, file_path in enumerate(all_paths):
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                continue

            # Determine file type and load accordingly
            if file_path.endswith('.parquet'):
                df = pd.read_parquet(file_path)
                if nrows:
                    df = df.head(nrows)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path, nrows=nrows)
            else:
                continue

            # Success - return the loaded data
            file_type = "primary" if i == 0 else "fallback"
            success_msg = f"✅ Loaded {description} from {file_type} source: {os.path.basename(file_path)}"
            return df, success_msg, None

        except FileNotFoundError:
            continue
        except pd.errors.ParserError as e:
            continue
        except Exception as e:
            continue

    # All loading attempts failed - create sample data
    return create_sample_data(description)


def create_sample_data(description="dataset"):
    """
    Create sample data when no data files are available.

    Args:
        description (str): Description of the dataset

    Returns:
        tuple: (sample_dataframe, success_message, warning_message)
    """
    # Create sample bulldozer data structure
    sample_data = {
        'SalesID': [1139246, 1139248, 1139249, 1139251, 1139253],
        'SalePrice': [66000, 57000, 10000, 38500, 11000],
        'YearMade': [2004, 1996, 1996, 2001, 1995],
        'MachineID': [999089, 117657, 434808, 1026470, 1057373],
        'ModelID': [3157, 77, 7009, 332, 17311],
        'ProductSize': ['Medium', 'Small', 'Small', 'Small', 'Small'],
        'state': ['Alabama', 'North Carolina', 'New York', 'Ohio', 'Oregon'],
        'ProductGroup': ['Track Type Tractor Dozers'] * 5,
        'Enclosure': ['EROPS', 'EROPS', 'EROPS', 'EROPS', 'EROPS']
    }

    df = pd.DataFrame(sample_data)

    success_msg = f"📊 Using sample {description} data for demonstration"
    warning_msg = f"⚠️ Original {description} files not available in deployment environment. Showing representative sample data structure."

    return df, success_msg, warning_msg


# ===== MAIN PAGE FUNCTION =====
# Main function to render the Project Framework page
def project_framework_body():
    # ===== PAGE HEADER =====
    st.subheader("*Forecasting Bulldozer Values Using Machine Learning*")
    st.write(
        """
        The **BulldozerPriceGenius (BPG)** project helps users predict bulldozer sale prices using machine learning. By analyzing historical sales data through a **time series regression model**, the app delivers accurate, data-driven valuations. Below is a diagram overview of the BPG project, and this page focuses on the **Cross Industry Standard Process for Data Mining (CRISP-DM)** workflow.
        """
    )
    safe_load_image(
        "static/images/BPG_Framework.webp",
        caption="BulldozerPriceGenius Project Framework",
        fallback_message="Project framework diagram showing the CRISP-DM workflow implementation for bulldozer price prediction, including data understanding, preparation, modeling, and evaluation phases."
    )

    # ===== NAVIGATION =====
    # Table of contents for easy navigation
    st.markdown(
        """
        - [1. Business Understanding](#1-business-understanding)
        - [2. Data Understanding](#2-data-understanding)
        - [3. Data Preparation](#3-data-preparation)
        - [4. Modeling](#4-modeling)
        - [5. Evaluation](#5-evaluation)
        - [6. Deployment](#6-deployment)
        - [Conclusion](#conclusion)
        """
    )
    st.write("---")
    st.header("1. Business Understanding")

    # Core business requirements
    st.subheader("Business Requirements")
    st.write(
        "The core business requirement for the BPG project that drives all decisions concerning the machine learning model is:"
    )
    st.success(
        """
        **Core Business Requirement**: Develop a machine learning model to accurately predict future sale prices of bulldozers with a Root Mean Squared Log Error (RMSLE) below 1.0.
        """
    )

    # Optional: Display detailed business requirements
    if st.checkbox("Show Business Requirements"):
        st.success(
            """
            **Business Requirement 1**: The client needs to understand what factors most significantly influence bulldozer auction prices to help optimize their auction strategies and provide better guidance to sellers and buyers.

            **Business Requirement 2**: The client requires a machine learning system that can accurately predict bulldozer prices based on historical auction data with a Root Mean Squared Log Error (RMSLE) score of below 1.0.

            **Business Requirement 3**: The client needs the prediction system to be accessible through a user-friendly interface that can be used by both technical and non-technical staff.
            """
        )

    st.subheader(
        "Here's a breakdown of how different stakeholders will be impacted by BulldozerPriceGenius:"
    )
    st.markdown(
        """
    **Buyers**:
    - Make more informed purchasing decisions by understanding fair market values
    - Filter and browse listings across U.S. states based on location and predicted prices
    - Reduce risk of overpaying for equipment

    **Sellers**:
    - Price bulldozers more accurately for auctions
    - Optimize timing and strategy for selling equipment
    - Avoid leaving money on the table through data-driven pricing

    **Auctioneers (Fast Iron)**:
    - Create a standardized pricing reference similar to Kelly Blue Book for bulldozers
    - Increase market transparency and efficiency
    - Improve buyer and seller confidence in auction processes

    **App Owner and Developers**:
    - Establish a valuable market position by providing essential pricing intelligence
    - Build trust through accurate predictions using comprehensive auction data analysis
    - Create recurring value through continuous model updates and market insights
    """
    )
    st.write("---")

    # ===== SECTION 2: DATA UNDERSTANDING =====
    st.header("2. Data Understanding")

    # Overview of available data
    st.subheader("What Data Do We Have?")
    st.write(
        """
        Our project uses three main datasets from [Kaggle](https://www.kaggle.com/c/bluebook-for-bulldozers/data):
        - **Training data**: Sales records up to `2011`
        - **Validation data**: Sales from `January to April 2012`
        - **Test data**: Sales from `May to November 2012`
        """
    )

    # Data quality check
    st.subheader("Data Quality Check")
    st.write(
        """
        The dataset has over `400,000` entries (bulldozer sales records).
        Here's what we found:

        - **Good Points:**
            - Large dataset with detailed information
            - Covers multiple years of sales
            - Contains various machine details
        - **Challenges:**
            - Some missing information in important fields
            - Mixed data types that need cleaning
            - Dates need to be converted to the right format
        """
    )

    # Load and display the dataset with safe loading
    primary_path = "src/data_prep/TrainAndValid_object_values_as_categories.csv"
    fallback_paths = [
        "data/processed/TrainAndValid_object_values_as_categories.csv",
        "data/processed/TrainAndValid_processed.csv"
    ]

    df, success_msg, error_msg = safe_load_data(
        primary_path,
        fallback_paths,
        nrows=500,
        description="training dataset for inspection"
    )

    if success_msg:
        st.info(success_msg)
    if error_msg:
        st.warning(error_msg)

    # Optional: Inspect missing values in the dataset
    if st.checkbox("DataFrame Inspection: Missing Values"):
        st.write("View the first `500` entries from the dataset")
        st.dataframe(df)

    # Optional: Inspect processed dataset for mixed data types
    if st.checkbox("DataFrame Inspection: Data Mixed Types"):
        # Load processed data with safe loading
        processed_primary = "data/processed/TrainAndValid_processed.csv"
        processed_fallbacks = [
            "data/processed/TrainAndValid_object_values_as_categories.csv"
        ]

        df_processed, proc_success_msg, proc_error_msg = safe_load_data(
            processed_primary,
            processed_fallbacks,
            nrows=500,
            description="processed dataset for data type inspection"
        )

        if proc_success_msg:
            st.info(proc_success_msg)
        if proc_error_msg:
            st.warning(proc_error_msg)

        st.write("This indicates that several columns contain mixed data types, where a single column might have both strings and integers, for example.")
        buffer = io.StringIO()
        df_processed.info(buf=buffer)
        s = buffer.getvalue()
        st.text(s)

    # Explanation of dataset structure
    st.subheader("What Each Part Means")
    if st.checkbox("Main Types of Information"):
        st.write("The dataset includes these main types of information:")
        st.info(
            """
            - **Basic Details**:
                - **Sales ID**: Unique number for each sale
                - **Machine ID**: Unique number for each bulldoer
                - **Sale Price**: How much the bulldozer sold for (this is what we want to predict)
            - **Machine Information**:
                - **Year Made**: When the bulldozer was built
                - **Usage Hours**: How many hours the machine has been used
                - **Usage Level**: Low, medium, or high based on hours used
            - **Sale Details**:
                - **Sale Date**: When the bulldozer was sold
                - **State**: Where the sale happened in the USA
            """
        )
    st.write("---")

    # ===== SECTION 3: DATA PREPARATION =====
    st.header("3. Data Preparation")

    # Data cleaning steps
    st.subheader("Data Cleaning")
    st.write(
        """
        1. **Parse Dates**
            - Convert `'saledate'` from string to datetime
            - Sort data chronologically
        2. **Handle Categorical Data**
            - Convert string columns to category type
            - Create numerical representations
        3. **Address Missing Values**
            - Identify columns with missing data
            - Apply appropriate imputation strategies
        """
    )

    # Optional: Check missing values in the dataset
    if st.checkbox("DataFrame Inspection: Identify columns with missing data"):
        st.write("This calculates the total missing values per column efficiently, rather than checking rows one by one.")

        # Safe data loading with fallback options
        primary_path = "data/processed/TrainAndValid_object_values_as_categories.parquet"
        fallback_paths = [
            "data/processed/TrainAndValid_object_values_as_categories.csv",
            "data/processed/TrainAndValid_processed.csv"
        ]

        df_tmp, success_msg, error_msg = safe_load_data(
            primary_path,
            fallback_paths,
            nrows=1000,  # Limit rows for performance
            description="training data for missing value analysis"
        )

        if success_msg:
            st.info(success_msg)
        if error_msg:
            st.warning(error_msg)

        # Calculate and display missing values
        missing_values = df_tmp.isna().sum().sort_values(ascending=False)[:25]
        st.write("**Top 25 columns with missing values:**")
        st.write(missing_values)

    # Feature engineering steps
    st.subheader("Feature Engineering")
    st.write(
        """
        1. **Date-based Features**
            - Extract year, month, day from saledate
            - Create day of week and day of year features
        2. **Categorical Encoding**
            - One-hot encoding for nominal categories
            - Label encoding for ordinal categories
        3. **Derived Features**
            - Calculate machine age at sale
            - Create usage intensity metrics
        """
    )

    # Data transformation steps
    st.subheader("Data Transformation")
    st.write(
        """
        1. **Scaling**
            - Normalize numerical features
            - Handle outliers appropriately
        2. **Final Processing**
            - Format data for model input
            - Split into training and validation sets
        3. **Quality Checks**
            - Verify data completeness
            - Validate transformations
        """
    )

    # Optional: Inspect random sample rows
    if st.checkbox("Quality Checks: Inspection of Random Sample Rows"):
        # Safe data loading with fallback options
        primary_path = "data/processed/TrainAndValid_object_values_as_categories_and_missing_values_filled.parquet"
        fallback_paths = [
            "data/processed/TrainAndValid_object_values_as_categories.parquet",
            "data/processed/TrainAndValid_object_values_as_categories.csv",
            "data/processed/TrainAndValid_processed.csv"
        ]

        df_tmp, success_msg, error_msg = safe_load_data(
            primary_path,
            fallback_paths,
            nrows=100,  # Limit rows for performance
            description="processed training data for quality inspection"
        )

        if success_msg:
            st.info(success_msg)
        if error_msg:
            st.warning(error_msg)

        # Display random sample
        st.write("**Random sample of 5 rows:**")
        if len(df_tmp) >= 5:
            st.write(df_tmp.sample(5))
        else:
            st.write(df_tmp)  # Show all rows if less than 5
    st.write("---")

    # ===== SECTION 4: MODELING =====
    st.header("4. Modeling")
    st.write(
        """
        This section focuses on selecting, training, and testing a suitable machine learning model to predict bulldozer sale prices.
        """
    )
    st.subheader("Choose Model Type")
    st.markdown(
        """
        - **Model Selection:** We picked the `Random Forest` model because it has worked well for similar projects before and is known to handle this type of data well.
        - **Choosing the Right Tool:** Given that we have a lot of data (over 100,000 examples) and need to predict prices, we looked at two main options:
            - `SGD Regressor`: A simple math model that learns by looking at one example at a time.
            - `Random Forest`: A more advanced model that uses multiple decision trees to make better predictions.
        - **Final Choice:** We went with the Random Forest model because it has a good track record with similar projects and usually gives reliable results across many different types of data.
        """
    )
    st.subheader("Train the Model")
    st.markdown(
        """
        **Data Preparation**: Before we can train our model, we need to prepare our data properly:
        """
    )
    st.markdown(
        """
        - **Convert to Numeric**: First, we need to turn all text data into numbers that our computer can understand. We do this by putting similar items into categories and giving each category a number.
        """
    )
    st.markdown(
        """
        - **Handling Missing Values**: Next, we look at any missing information in our data. When we find gaps, we either fill them in with reasonable values or use special techniques to work around them.
        """
    )
    st.markdown(
        """
        **Training Process**: The RandomForestRegressor is trained using the preprocessed data.
        """
    )
    st.markdown(
        """
        **Hyperparameter Tuning:** Parameters like the number of trees in the Random Forest can be optimized for better performance.
        """
    )
    st.subheader("Test the Model")
    st.markdown(
        """
        - **Evaluation Metric**: We use the Root Mean Squared Log Error (RMSLE) as our evaluation metric, which aligns with the Kaggle competition and sets a target accuracy goal of under 1 RMSLE.
        """
    )
    st.markdown(
        """
        - **Validation Set**: Allows for assessment of the model's generalization performance on unseen data.
        """
    )
    st.markdown(
        """
        - **Performance Comparison**: The RMSLE obtained would be compared against the Kaggle leaderboard to benchmark the model's effectiveness.
        """
    )
    st.markdown(
        """
        - **Further Testing**: After we test our model on the validation data, we'll do one final test using a separate set of data we've kept aside. This helps us better understand how well our model will work in real-life situations.
        """
    )
    st.write("---")

    # ===== SECTION 5: EVALUATION =====
    st.header("5. Evaluation")
    st.subheader("Did We Meet Our Goals?")
    st.write("**Business Requirement 1:**")
    st.markdown(
        """
        - **Goal Achievement:** The client needs to understand what factors most significantly influence bulldozer auction prices to help optimize their auction strategies and provide better guidance to sellers and buyers.
        - **Achievement Status:** Yes, this goal was successfully achieved. The Random Forest model provided clear insights into the top 20 feature importance values for Best RandomForestRegressor Model.
        """
    )

    if st.checkbox("Inspection: Feature Importance"):
        safe_load_image(
            "results/feature_importance.webp",
            caption="Feature Importance Analysis",
            fallback_message="Feature importance analysis shows YearMade (19.9%), ProductSize (15.5%), and ModelID (12.8%) as the top predictors, confirming these factors significantly influence bulldozer prices."
        )

    # Define the DataFrame
    data = {
        "Feature": [
            "Year Made",
            "Product Size",
            "Sale Year",
            "Model Description",
            "Model ID",
            "Other Features",
        ],
        "Importance": [
            19.9,
            15.5,
            7.7,
            5.7,
            5.6,
            45.6,
        ],  # Numeric values for percentages
    }
    df = pd.DataFrame(data)

    # Add a checkbox to display the feature importance chart
    if st.checkbox("Inspection: Top 5 Feature Importance Chart"):
        # Display feature importance using Streamlit's native bar chart
        # This replaces matplotlib pie chart to avoid dependency issues

        st.subheader("📊 Feature Importance Distribution")

        # Create a horizontal bar chart using Streamlit's native functionality
        chart_data = df.set_index('Feature')
        st.bar_chart(chart_data['Importance'])

        # Display the data table for reference
        st.subheader("📋 Feature Importance Values")
        st.dataframe(df, use_container_width=True)
        st.subheader("**Analysis of Top Features**")
        st.markdown(
            """
            **Primary Features:**
            - **Year Made** (`19.9%`): The most significant factor, with newer bulldozers commanding higher prices.
            - **Product Size** (`15.5%`): Second most important, larger machines typically cost more.

            **Secondary Features:**
            - **Sale Year** (`7.7%`): Reflects market conditions at time of sale.
            - **Model Description** (`5.7%`): Specific model features impact pricing.
            - **Model ID** (`5.6%`): Different models have varying base prices.
            """
        )

    st.write("**Business Requirement 2:**")
    st.markdown(
        """
        - **Goal Achievement:** The project aims to predict bulldozer sale prices based on their characteristics and historical data. Success is measured using the **Root Mean Squared Log Error (RMSLE)**, with a target benchmark of achieving a score below `1.0`.
        - **Achievement Status:** The project successfully achieved its goal with an impressive RMSLE score of `0.27`, significantly outperforming the target benchmark of `1.0`.
        """
    )

    # Add a checkbox to display the output
    if st.checkbox("Inspection: Prediction vs Reality Analysis"):
        # Display the image with safe loading
        safe_load_image(
            "results/sale_price.webp",
            caption="Prediction vs Reality Analysis",
            fallback_message="Price prediction analysis demonstrates model accuracy with RMSLE of 0.27. The comparison shows predicted vs actual prices, validating the model's reliability for bulldozer price estimation."
        )

        # Display price comparison metrics
        st.subheader("Prediction vs Reality")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Model Prediction", f"${55495.68:,.2f}")
        with col2:
            st.metric("Actual Price", f"${72600:,.2f}")

        # Show error metrics
        st.subheader("Performance Metrics")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Mean Absolute Error (MAE)", f"${17104:,.2f}")
        with col2:
            st.metric("RMSLE Score", "0.27")

        # Analysis of results
        st.subheader("Analysis")
        st.write(
            """
            - RMSLE score of `0.27` indicates reasonable model performance.
            - Model provides valuable pricing guidance.
            - Some room for improvement exists.
            """
        )

        # Price accuracy conclusions
        st.subheader("Conclusion")
        st.write(
            """
            Yes, our hypothesis was validated. Our target **RMSLE score** was below `1.0`, and we achieved `0.27` — **significantly exceeding our expectations**. While we've met our goal, we can still work on reducing the `$17,104` average error to make our predictions even more precise. Users can trust the model's price estimates.
            **What does this mean?**
            - Our predictions are more accurate than expected.
            - Users can trust our model for pricing guidance.
            - The system is ready for real-world use.
            """
        )

    st.write("**Business Requirement 3:**")
    st.markdown(
        """
        - **Goal Achievement:** The user needs the prediction system to be accessible through a user-friendly interface that can be used by both technical and non-technical staff.
        - **Achievement Status:** Yes, this goal was achieved through the development of an intuitive dashboard that allows users to filter bulldozer entries by price range and U.S. state location.
        """
    )
    # Add a checkbox to display the image
    if st.checkbox("Inspection: Interactive Dashboard Image"):
        safe_load_image(
            "static/images/interactive_dashboard.webp",
            caption="Interactive Dashboard Interface",
            fallback_message="Interactive dashboard interface showing user-friendly bulldozer filtering capabilities by price range and location, demonstrating the accessible interface for both technical and non-technical users."
        )
    st.write(
        """
        The dashboard shows a filtered list of bulldozers based on price and location.
        For example, you can see bulldozers in California priced between `$28,771` and `$109516`.
        """
    )

    st.subheader("Is It Good Enough?")
    st.markdown(
        """
        - **Stakeholder Requirements:** The app exceeded its target requirement of achieving an RMSLE score below `1.0`. In the context of the Kaggle Competition leaderboard, which included 428 entries, our model achieved an RMSLE score of `0.27`—ranking 69th overall. For comparison, the top score in the competition was `0.22909`.
        """
    )

    # Add a checkbox to display the Kaggle leaderboard image
    if st.checkbox("Inspection: Kaggle Leaderboard"):
        safe_load_image(
            "static/images/kaggle_leaderboard.webp",
            caption="Kaggle Competition Leaderboard",
            fallback_message="Kaggle leaderboard showing our model's performance ranking 69th out of 428 entries with RMSLE of 0.27, demonstrating competitive performance in the bulldozer price prediction challenge."
        )
        st.write(
            "[*Kaggle Leaderboard*](https://www.kaggle.com/c/bluebook-for-bulldozers/leaderboard)"
        )

    st.markdown(
        """
        - **Cost-Benefit Analysis:** Since the data is several years old, further improvements to the app's accuracy would offer limited value without more recent data. With current data and additional development time, the model's prediction accuracy could improve significantly, potentially advancing our position on the Kaggle Leaderboard and increasing its value to the customer.
        """
    )

    st.subheader("What Are The Areas for Possible Improvement?")
    st.markdown(
        """
        - **Data Cleaning:** We found some missing information in our data. We could improve our results by using special tools that work well with incomplete data.
        - **Adding Better Data Features:** We only used basic time-related information in our analysis. We could make our predictions better by combining existing data in new ways or adding more relevant information about bulldozers.
        - **Trying Different Tools:** We used a tool called Random Forest for our predictions. We could test other tools like CatBoost or XGBoost to see if they work better for our needs.
        """
    )

    st.write("---")

    # ===== SECTION 6: DEPLOYMENT =====
    st.header("6. Deployment")
    st.write(
        """
        The application is deployed and hosted on both [Streamlit](https://streamlit.io/) Cloud and [Heroku](https://www.heroku.com/?utm_source=google&utm_medium=paid_search&utm_campaign=emea_heraw&utm_content=general-branded-search-rsa&utm_term=heroku&utm_source_platform=GoogleAds&gad_source=1&gclid=CjwKCAjwnPS-BhBxEiwAZjMF0s32zmesSen1_nAdsUsoJls9kZQ89I_Rn-alHDSfSWniSlB03TYbfxoCCF8QAvD_BwE) platforms
        """
    )
    st.write("---")

    # ===== SECTION 7: Conclusion =====
    st.header("Conclusion")
    st.write("**Data Project Success Fundamentals**")
    st.write(
        """
        **Project Success Overview**

        The BulldozerPriceGenius project has successfully met all three business requirements, as demonstrated by the validation results on this page and the previous Hypothesis and Validation page. The project's success is attributed to the following key factors:

        **Model Performance**

        Using a Random Forest regression model, the project achieved its primary success metric with an RMSLE score of `0.27`, surpassing the target threshold of `1.0`. The secondary target of **Mean Absolute Error (MAE)** within `$20,000` of actual prices was also met, achieving an average error of `$17,104`.

        **Business Impact**

        The model's feature importance analysis revealed key factors influencing bulldozer prices, providing valuable insights for auction strategies. A user-friendly interface and interactive dashboard ensure accessibility for all users, meeting client requirements.

        **Validation and Future Potential**

        The project's ranking of `69th` out of `428` entries on the **Kaggle leaderboard** further validates its success. While model accuracy could be improved with additional data and feature enhancements, current results show the system is ready for real-world deployment. The project demonstrates the value of data-driven insights in optimizing auction strategies and providing accurate bulldozer price predictions.
        """
    )
    st.write("---")


# ===== MAIN EXECUTION =====
if __name__ == "__main__":
    project_framework_body()
