#!/usr/bin/env python3
"""
Test core application imports with optimized dependencies
"""

def test_core_imports():
    """Test that core application can import all required dependencies"""
    
    print("Testing Core Application Imports with Optimized Dependencies")
    print("=" * 70)
    
    try:
        import streamlit as st
        print("✅ streamlit imported successfully")
        
        import numpy as np
        print("✅ numpy imported successfully")
        
        import pandas as pd
        print("✅ pandas imported successfully")
        
        import sklearn
        print("✅ scikit-learn imported successfully")
        
        import requests
        print("✅ requests imported successfully")
        
        import gdown
        print("✅ gdown imported successfully")
        
        print("\n🎯 All core dependencies available!")
        print("✅ Application should work with optimized requirements.txt")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("⚠️  May need to add missing dependency back to requirements.txt")
        return False

if __name__ == "__main__":
    test_core_imports()
