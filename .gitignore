# Environment variables and secrets (SECURITY CRITICAL)
.env
.env.*
.streamlit/secrets.toml
config.ini
credentials.json

# Documentation files (exclude from version control)
docs/

# Rubric and grading files (maintain confidentiality of assessment criteria)
rubric*
*rubric*
*.rubric
RUBRIC*
grading_rubric*
assessment_rubric*

# Virtual environments (should never be in version control)
myenv/
venv/
env/
.env/
.venv/
ENV/
env.bak/
venv.bak/

# Large data files (use external storage for deployment)
data/raw/
data/processed/*.csv
data/processed/*.parquet
src/models/*.pkl
src/models/*.joblib
src/models/*.h5
src/models/*.pt

# Temporary and cache files
core.Microsoft*
core.mongo*
core.python*
env.py
__pycache__/
*.py[cod]
node_modules/
.github/
cloudinary_python.txt
kaggle.json
*.key
*.pem
*.p12
*.pfx

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db